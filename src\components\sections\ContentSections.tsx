import React from "react";
import Container from "../Container";

// Hero Section Component
export function Hero() {
  return (
    <section className="py-16 sm:py-24 bg-background" aria-labelledby="hero-heading">
      <Container className="text-center">
        <div className="inline-flex items-center rounded-full border px-3 py-1 text-xs font-medium gap-2" role="status" aria-label="New template announcement">
          <span className="inline-block h-2 w-2 rounded-full bg-emerald-500" aria-hidden="true" />
          New template • Next.js + Tailwind
        </div>
        <h1 id="hero-heading" className="mt-6 text-4xl sm:text-5xl md:text-6xl font-heading font-semibold tracking-tight">
          Jadikan brand Anda menonjol dengan landing page modern
        </h1>
        <p className="mt-4 text-base sm:text-lg text-muted-foreground max-w-2xl mx-auto">
          Template responsif, cepat, dan mudah dikustomisasi. Cocok untuk bisnis, produk, atau kampanye.
        </p>
        <div className="mt-8 flex items-center justify-center gap-3" role="group" aria-label="Call to action buttons">
          <a
            href="#contact"
            className="inline-flex h-11 items-center justify-center rounded-md bg-primary-button-bg text-primary-button-text px-5 text-sm font-medium hover:bg-primary-button-hover transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-primary-button-bg"
            aria-label="Start using the template now"
          >
            Mulai Sekarang
          </a>
          <a
            href="#gallery"
            className="inline-flex h-11 items-center justify-center rounded-md border border-secondary-button-border bg-secondary-button-bg text-secondary-button-text px-5 text-sm font-medium hover:bg-secondary-button-hover transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-secondary-button-border"
            aria-label="View template demo and examples"
          >
            Lihat Demo
          </a>
        </div>
      </Container>
    </section>
  );
}

// Key Benefits Section Component
const benefits = [
  {
    title: "Cepat & SEO-friendly",
    desc: "Dibangun dengan Next.js 15 dan optimasi modern.",
  },
  {
    title: "Mudah dikustomisasi",
    desc: "Tailwind CSS memudahkan styling yang konsisten.",
  },
  {
    title: "Responsif",
    desc: "Tampilan optimal di semua perangkat.",
  },
];

export function KeyBenefits() {
  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">Key Benefits</h2>
        <div className="mt-8 grid gap-6 sm:grid-cols-3">
          {benefits.map((b) => (
            <div key={b.title} className="rounded-xl border p-6 bg-white/50 dark:bg-zinc-900/50">
              <h3 className="font-heading font-semibold text-lg">{b.title}</h3>
              <p className="mt-2 text-sm text-muted-foreground">{b.desc}</p>
            </div>
          ))}
        </div>
      </Container>
    </section>
  );
}

// About Section Component
export function About() {
  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">Tentang Kami</h2>
        <div className="mt-4 grid gap-6 sm:grid-cols-2 items-start">
          <p className="text-sm text-muted-foreground">
            Kami adalah tim yang fokus pada pengalaman pengguna dan performa.
            Template ini dirancang untuk memudahkan Anda meluncurkan landing page berkualitas tinggi.
          </p>
          <div className="rounded-xl border p-6">
            <h3 className="font-heading font-semibold">Misi</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              Membantu bisnis tumbuh lewat web yang cepat, indah, dan efektif.
            </p>
          </div>
        </div>
      </Container>
    </section>
  );
}

// Location & Hours Section Component
export function LocationHours() {
  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">Lokasi & Jam Operasional</h2>
        <div className="mt-6 grid gap-6 sm:grid-cols-2">
          <div className="rounded-xl border p-6">
            <h3 className="font-heading font-semibold">Alamat</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              Jl. Contoh No. 123, Jakarta, Indonesia
            </p>
            <div className="mt-4">
              <a
                href="#"
                className="inline-flex h-10 items-center justify-center rounded-md border border-secondary-button-border bg-secondary-button-bg text-secondary-button-text px-4 text-sm hover:bg-secondary-button-hover transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-secondary-button-border"
              >
                Lihat di Google Maps
              </a>
            </div>
          </div>
          <div className="rounded-xl border p-6">
            <h3 className="font-heading font-semibold">Jam Operasional</h3>
            <ul className="mt-2 text-sm text-muted-foreground space-y-1">
              <li>Senin - Jumat: 09:00 - 18:00</li>
              <li>Sabtu: 10:00 - 16:00</li>
              <li>Minggu & Hari Libur: Tutup</li>
            </ul>
          </div>
        </div>
      </Container>
    </section>
  );
}
